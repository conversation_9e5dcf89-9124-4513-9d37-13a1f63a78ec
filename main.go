package main

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"math"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/gorilla/websocket"
	"github.com/hirokisan/bybit/v2"
	"github.com/joho/godotenv"
	"github.com/valyala/fasthttp"
)

// Глобальные переменные для границ отклонения
var (
	mediumPrice          float64
	bidDeviationPriceMin float64 // нижняя граница минимального отклонения
	askDeviationPriceMin float64 // верхняя граница минимального отклонения
	bidDeviationPriceMax float64 // нижняя граница максимального отклонения
	askDeviationPriceMax float64 // верхняя граница максимального отклонения
)

// Структура для отслеживания ордеров
type OrderInfo struct {
	OrderID   string
	Price     float64
	Side      string
	Timestamp time.Time
}

// Глобальные переменные для новой функциональности
var (
	maxPriceDeviationBid                float64 // динамическое MAX_PRICE_DEVIATION для bids
	maxPriceDeviationAsk                float64 // динамическое MAX_PRICE_DEVIATION для asks
	enableMaxPriceDeviationReduction    bool
	cancelOldOrders                     string
	cancelTimeoutSeconds                float64
	activeOrders                        []OrderInfo
	activeOrdersMutex                   sync.Mutex
	lastManipulatorOrderPriceBid        float64
	lastManipulatorOrderPriceAsk        float64
	shutdownRequested                   bool
	shutdownMutex                       sync.Mutex
	wg                                  sync.WaitGroup
)

// Переменные окружения
var (
	symbol                string
	qtyUsdt               int
	qtyProgrevUsdt        int
	progrevPercent        float64
	progrevLoop           bool
	progrevInterval       int
	volPerelivashaUsdt    float64
	multiplierPerelivasha float64
	shagCeni              float64
	shagCeniMultiplier    float64
	nulAfter              int
	minOrderQty           float64
	qtyStep               float64
	apiKey                string
	apiSecret             string
	orderPlaced           = 0
	lastOderType          string
	coinPriceInUsdt       float64
	bybitClient           *bybit.Client
	maxPriceDeviation     float64
	minPriceDeviation     float64
	maxOrders             int
	useLevel1             bool
	useLevel50            bool
	useLevel200           bool
	directApi             bool
	recvWindow            string
	exchangeEndpoint      = "https://api.bybit.com"
)

// Функция для создания подписи
func createSignature(queryString, secret string) string {
	hmac256 := hmac.New(sha256.New, []byte(secret))
	hmac256.Write([]byte(queryString))
	return hex.EncodeToString(hmac256.Sum(nil))
}

// Функция для получения значения из переменных окружения или командной строки
func getEnvOrDefault(_, envVar, defaultValue string) string {
	if val := os.Getenv(envVar); val != "" {
		return val
	}
	return defaultValue
}

// Функция для получения целого числа
func getIntEnvOrDefault(_, envVar string, defaultValue int) int {
	if val := os.Getenv(envVar); val != "" {
		if num, err := strconv.Atoi(val); err == nil {
			return num
		}
	}
	return defaultValue
}

// Функция для получения вещественного числа
func getFloatEnvOrDefault(_, envVar string, defaultValue float64) float64 {
	if val := os.Getenv(envVar); val != "" {
		if num, err := strconv.ParseFloat(val, 64); err == nil {
			return num
		}
	}
	return defaultValue
}

// Функция для получения булевого значения
func getBoolEnvOrDefault(_, envVar string, defaultValue bool) bool {
	if val := os.Getenv(envVar); val != "" {
		if b, err := strconv.ParseBool(val); err == nil {
			return b
		}
	}
	return defaultValue
}

// Функция для получения строкового значения
func getStringEnvOrDefault(_, envVar, defaultValue string) string {
	if val := os.Getenv(envVar); val != "" {
		return val
	}
	return defaultValue
}

// Функция для получения информации о символе
func getSymbolInfo(symbol string) (int, float64, float64, float64, error) {
	client := bybit.NewClient().WithAuth(apiKey, apiSecret)

	symbolV5 := bybit.SymbolV5(symbol)
	resp, err := client.V5().Market().GetInstrumentsInfo(bybit.V5GetInstrumentsInfoParam{
		Category: bybit.CategoryV5Linear,
		Symbol:   &symbolV5,
	})
	if err != nil {
		return 0, 0, 0, 0, fmt.Errorf("ошибка получения информации о символе: %v", err)
	}

	if len(resp.Result.LinearInverse.List) == 0 {
		return 0, 0, 0, 0, fmt.Errorf("информация о символе %s не найдена", symbol)
	}

	info := resp.Result.LinearInverse.List[0]
	log.Printf("Полная информация о символе:")
	log.Printf("LotSizeFilter: %+v", info.LotSizeFilter)
	log.Printf("PriceFilter: %+v", info.PriceFilter)

	priceScale, err := strconv.Atoi(info.PriceScale)
	if err != nil {
		return 0, 0, 0, 0, fmt.Errorf("ошибка преобразования priceScale: %v", err)
	}

	minPrice, err := strconv.ParseFloat(info.PriceFilter.MinPrice, 64)
	if err != nil {
		return 0, 0, 0, 0, fmt.Errorf("ошибка преобразования minPrice: %v", err)
	}

	minOrderQty, err := strconv.ParseFloat(info.LotSizeFilter.MinOrderQty, 64)
	if err != nil {
		return 0, 0, 0, 0, fmt.Errorf("ошибка преобразования minOrderQty: %v", err)
	}

	qtyStep, err := strconv.ParseFloat(info.LotSizeFilter.QtyStep, 64)
	if err != nil {
		return 0, 0, 0, 0, fmt.Errorf("ошибка преобразования qtyStep: %v", err)
	}

	return priceScale, minPrice, minOrderQty, qtyStep, nil
}

// Цвета для стилизации текста
const (
	Reset  = "\033[0m"
	Bold   = "\033[1m"
	Cyan   = "\033[36m"
	Yellow = "\033[33m"
	Red    = "\033[31m"
	Green  = "\033[32m"
)

func init() {
	// Загружаем переменные из .env
	if err := godotenv.Load(); err != nil {
		log.Fatal("Ошибка загрузки .env файла")
	}

	// Определяем флаги и получаем значения из окружения или по умолчанию
	flag.StringVar(&symbol, "symbol", getEnvOrDefault("symbol", "SYMBOL", ""), "Торговая пара")
	flag.IntVar(&qtyUsdt, "qty_usdt", getIntEnvOrDefault("qty_usdt", "QTY_USDT", 0), "Количество в USDT")
	flag.IntVar(&qtyProgrevUsdt, "qty_progrev_usdt", getIntEnvOrDefault("qty_progrev_usdt", "QTY_PROGREV_USDT", 10), "Количество в USDT для прогрева (по умолчанию 10)")
	flag.Float64Var(&progrevPercent, "progrev_percent", getFloatEnvOrDefault("progrev_percent", "PROGREV_PERCENT", 10), "Процент ниже средней цены для прогревочного ордера (по умолчанию 10)")
	flag.BoolVar(&progrevLoop, "progrev_loop", getBoolEnvOrDefault("progrev_loop", "PROGREV_LOOP", true), "Циклический режим прогревочного ордера (по умолчанию true)")
	flag.IntVar(&progrevInterval, "progrev_interval", getIntEnvOrDefault("progrev_interval", "PROGREV_INTERVAL", 5), "Интервал между прогревочными ордерами в секундах (по умолчанию 5)")
	flag.Float64Var(&volPerelivashaUsdt, "vol_perelivasha_usdt", getFloatEnvOrDefault("vol_perelivasha_usdt", "VOL_PERELIVASHA_USDT", 0), "Объем перелива в USDT")
	flag.Float64Var(&multiplierPerelivasha, "multiplier_perelivasha", getFloatEnvOrDefault("multiplier_perelivasha", "MULTIPLIER_PERELIVASHA", 0.7), "Процент от объема перелива для срабатывания (по умолчанию 0.7)")
	flag.Float64Var(&shagCeniMultiplier, "shag_ceni_multiplier", getFloatEnvOrDefault("shag_ceni_multiplier", "SHAG_CENI_MULTIPLIER", 1.0), "Множитель для шага цены (по умолчанию 1)")
	flag.Float64Var(&maxPriceDeviation, "max_price_deviation", getFloatEnvOrDefault("max_price_deviation", "MAX_PRICE_DEVIATION", 1.0), "Максимальное отклонение цены от средней в процентах (по умолчанию 1.0%)")
	flag.Float64Var(&minPriceDeviation, "min_price_deviation", getFloatEnvOrDefault("min_price_deviation", "MIN_PRICE_DEVIATION", 0.2), "Минимальное отклонение цены от средней в процентах (по умолчанию 0.2%)")
	flag.IntVar(&maxOrders, "max_orders", getIntEnvOrDefault("max_orders", "MAX_ORDERS", 2), "Максимальное количество выставленных ордеров (по умолчанию 2)")
	flag.BoolVar(&useLevel1, "use_level1", true, "Использовать Level 1 (глубина 1, частота 10мс)")
	flag.BoolVar(&useLevel50, "use_level50", true, "Использовать Level 50 (глубина 50, частота 20мс)")
	flag.BoolVar(&useLevel200, "use_level200", false, "Использовать Level 200 (глубина 200, частота 100мс)")
	flag.BoolVar(&directApi, "direct_api", false, "Использовать fasthttp вместо библиотеки bybit (по умолчанию false)")
	flag.StringVar(&recvWindow, "recv_window", getEnvOrDefault("recv_window", "RECV_WINDOW", "1000"), "Receive window для API запросов в мс (по умолчанию 1000)")
	flag.BoolVar(&enableMaxPriceDeviationReduction, "enable_max_price_deviation_reduction", getBoolEnvOrDefault("enable_max_price_deviation_reduction", "ENABLE_MAX_PRICE_DEVIATION_REDUCTION", true), "Включить уменьшение MAX_PRICE_DEVIATION после исполнения ордеров манипулятора (по умолчанию true)")
	flag.StringVar(&cancelOldOrders, "cancel_old_orders", getStringEnvOrDefault("cancel_old_orders", "CANCEL_OLD_ORDERS", "remove_after_timeout"), "Управление старыми ордерами: remove, keep, remove_after_timeout (по умолчанию remove_after_timeout)")
	flag.Float64Var(&cancelTimeoutSeconds, "cancel_timeout_seconds", getFloatEnvOrDefault("cancel_timeout_seconds", "CANCEL_TIMEOUT_SECONDS", 1.5), "Время ожидания перед снятием ордера в секундах (по умолчанию 1.5)")
	flag.StringVar(&apiKey, "api_key", getEnvOrDefault("api_key", "API_KEY", ""), "API ключ")
	flag.StringVar(&apiSecret, "api_secret", getEnvOrDefault("api_secret", "API_SECRET", ""), "API секрет")

	// Парсинг флагов
	flag.Parse()

	// Проверяем обязательные параметры
	if symbol == "" || qtyUsdt == 0 || volPerelivashaUsdt == 0 || apiKey == "" || apiSecret == "" {
		log.Fatal("Все параметры должны быть заданы")
	}

	// Валидация новых параметров
	if cancelOldOrders != "remove" && cancelOldOrders != "keep" && cancelOldOrders != "remove_after_timeout" {
		log.Fatal("Параметр cancel_old_orders должен быть одним из: remove, keep, remove_after_timeout")
	}

	if cancelTimeoutSeconds < 0 {
		log.Fatal("Параметр cancel_timeout_seconds должен быть положительным числом")
	}

	// Инициализируем динамические MAX_PRICE_DEVIATION значения
	maxPriceDeviationBid = maxPriceDeviation
	maxPriceDeviationAsk = maxPriceDeviation

	// Получаем информацию о символе из API
	var err error
	nulAfter, shagCeni, minOrderQty, qtyStep, err = getSymbolInfo(symbol)
	if err != nil {
		log.Fatalf("Ошибка получения информации о символе: %v", err)
	}

	log.Printf("Информация о символе %s:", symbol)
	log.Printf("Количество знаков после запятой: %d", nulAfter)
	log.Printf("Минимальный шаг цены: %f", shagCeni)
	log.Printf("Минимальное количество ордера: %f", minOrderQty)
	log.Printf("Шаг количества ордера: %f", qtyStep)

	bybitClient = bybit.NewClient().WithAuth(apiKey, apiSecret)
}

// Функция для снятия ордера
func cancelOrder(orderID string) error {
	if directApi {
		return cancelOrderDirect(orderID)
	}

	// Используем библиотеку bybit
	_, err := bybitClient.V5().Order().CancelOrder(bybit.V5CancelOrderParam{
		Category: bybit.CategoryV5Linear,
		Symbol:   bybit.SymbolV5(symbol),
		OrderID:  &orderID,
	})

	if err != nil {
		log.Printf("Ошибка снятия ордера %s: %v", orderID, err)
		return err
	}

	log.Printf("Ордер %s успешно снят", orderID)
	return nil
}

// Снятие ордера с использованием fasthttp
func cancelOrderDirect(orderID string) error {
	timestamp := time.Now().UnixNano() / 1000000
	params := map[string]interface{}{
		"category": "linear",
		"symbol":   symbol,
		"orderId":  orderID,
	}

	jsonData, err := json.Marshal(params)
	if err != nil {
		return fmt.Errorf("ошибка при маршалинге параметров: %v", err)
	}

	queryString := fmt.Sprintf("api_key=%s&category=linear&orderId=%s&recv_window=%s&symbol=%s&timestamp=%d",
		apiKey, orderID, recvWindow, symbol, timestamp)

	signature := createSignature(queryString, apiSecret)

	req := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(req)

	req.SetRequestURI(exchangeEndpoint + "/v5/order/cancel")
	req.Header.SetMethod("POST")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-BAPI-API-KEY", apiKey)
	req.Header.Set("X-BAPI-SIGN", signature)
	req.Header.Set("X-BAPI-TIMESTAMP", strconv.FormatInt(timestamp, 10))
	req.Header.Set("X-BAPI-RECV-WINDOW", recvWindow)
	req.SetBody(jsonData)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	client := &fasthttp.Client{}
	if err := client.Do(req, resp); err != nil {
		return fmt.Errorf("ошибка HTTP запроса: %v", err)
	}

	if resp.StatusCode() != 200 {
		return fmt.Errorf("HTTP статус: %d, тело ответа: %s", resp.StatusCode(), resp.Body())
	}

	var result map[string]interface{}
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return fmt.Errorf("ошибка разбора ответа: %v", err)
	}

	if retCode, ok := result["retCode"].(float64); ok && retCode != 0 {
		return fmt.Errorf("ошибка API: %v", result["retMsg"])
	}

	log.Printf("Ордер %s успешно снят через fasthttp", orderID)
	return nil
}

// Функция для обработки старых ордеров согласно настройкам
func handleOldOrders() {
	switch cancelOldOrders {
	case "remove":
		// Немедленно снимаем все активные ордера
		for i := len(activeOrders) - 1; i >= 0; i-- {
			order := activeOrders[i]
			err := cancelOrder(order.OrderID)
			if err == nil || strings.Contains(err.Error(), "order not exists") {
				// Удаляем из списка активных ордеров (успешно снят или уже не существует)
				activeOrders = append(activeOrders[:i], activeOrders[i+1:]...)
			}
		}
	case "keep":
		// Ничего не делаем, оставляем все ордера
		return
	case "remove_after_timeout":
		// Снимаем ордера, которые старше заданного времени
		now := time.Now()
		for i := len(activeOrders) - 1; i >= 0; i-- {
			order := activeOrders[i]
			if now.Sub(order.Timestamp).Seconds() >= cancelTimeoutSeconds {
				err := cancelOrder(order.OrderID)
				if err == nil || strings.Contains(err.Error(), "order not exists") {
					// Удаляем из списка активных ордеров (успешно снят или уже не существует)
					activeOrders = append(activeOrders[:i], activeOrders[i+1:]...)
				}
			}
		}
	}
}

// HTTP-запрос для размещения ордера
func placeOrder(price float64, qty int, side string) {
	// Обрабатываем старые ордера перед размещением нового
	handleOldOrders()

	startTime := time.Now()
	priceStr := fmt.Sprintf("%.8f", price)

	if directApi {
		// Используем fasthttp
		orderId, err := placeOrderDirect(price, qty, side)
		if err != nil {
			log.Fatalf("Ошибка создания ордера через fasthttp: %v", err)
		}

		// Добавляем ордер в список активных
		activeOrders = append(activeOrders, OrderInfo{
			OrderID:   orderId,
			Price:     price,
			Side:      side,
			Timestamp: time.Now(),
		})

		orderPlacementTime := float64(time.Since(startTime).Milliseconds()) + (float64(time.Since(startTime).Nanoseconds()%1e6) / 1e6)
		log.Printf("Ордер %s выставлен за %.2f ms через fasthttp", orderId, orderPlacementTime)
		return
	}

	// Используем библиотеку bybit
	timeInForce := bybit.TimeInForce("GTC")
	sideEnum := bybit.SideBuy
	if side == "Sell" {
		sideEnum = bybit.SideSell
	}

	log.Printf("Отправка ордера: цена=%s, количество=%d, сторона=%s", priceStr, qty, side)

	resp, err := bybitClient.V5().Order().CreateOrder(bybit.V5CreateOrderParam{
		Category:    bybit.CategoryV5Linear,
		Symbol:      bybit.SymbolV5(symbol),
		Side:        sideEnum,
		OrderType:   bybit.OrderTypeLimit,
		Qty:         fmt.Sprintf("%d", qty),
		Price:       &priceStr,
		TimeInForce: &timeInForce,
	})

	if err != nil {
		if resp != nil {
			log.Printf("Детали ошибки от API: %+v", resp)
		}
		log.Fatalf("Ошибка создания ордера: %v", err)
	}

	// Добавляем ордер в список активных
	if resp.Result.OrderID != "" {
		activeOrders = append(activeOrders, OrderInfo{
			OrderID:   resp.Result.OrderID,
			Price:     price,
			Side:      side,
			Timestamp: time.Now(),
		})
	}

	orderPlacementTime := float64(time.Since(startTime).Milliseconds()) + (float64(time.Since(startTime).Nanoseconds()%1e6) / 1e6)
	log.Printf("Ордер %s выставлен за %.2f ms", resp.Result.OrderID, orderPlacementTime)
}

// Размещение ордера с использованием fasthttp
func placeOrderDirect(price float64, qty int, side string) (string, error) {
	timestamp := time.Now().UnixNano() / 1000000
	params := map[string]interface{}{
		"category":    "linear",
		"symbol":      symbol,
		"side":        side,
		"orderType":   "Limit",
		"qty":         strconv.Itoa(qty),
		"price":       strconv.FormatFloat(price, 'f', 8, 64),
		"timeInForce": "GTC",
	}

	jsonData, err := json.Marshal(params)
	if err != nil {
		return "", fmt.Errorf("ошибка при маршалинге параметров: %v", err)
	}

	// Генерация подписи
	hmac256 := hmac.New(sha256.New, []byte(apiSecret))
	hmac256.Write([]byte(fmt.Sprintf("%d%s%s%s", timestamp, apiKey, recvWindow, string(jsonData))))
	signature := hex.EncodeToString(hmac256.Sum(nil))

	// Создание запроса
	req := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(req)

	req.SetRequestURI(exchangeEndpoint + "/v5/order/create")
	req.Header.SetMethod("POST")
	req.Header.SetContentType("application/json")
	req.Header.Set("X-BAPI-API-KEY", apiKey)
	req.Header.Set("X-BAPI-SIGN", signature)
	req.Header.Set("X-BAPI-TIMESTAMP", strconv.FormatInt(timestamp, 10))
	req.Header.Set("X-BAPI-RECV-WINDOW", recvWindow)
	req.SetBody(jsonData)

	// Получение объекта ответа из пула
	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	// Выполнение запроса с таймаутом
	if err := fasthttp.DoTimeout(req, resp, 5*time.Second); err != nil {
		return "", fmt.Errorf("ошибка при выполнении запроса: %v", err)
	}

	// Проверка статуса ответа
	if resp.StatusCode() != fasthttp.StatusOK {
		return "", fmt.Errorf("неожиданный статус ответа: %d", resp.StatusCode())
	}

	// Разбор ответа
	var response map[string]interface{}
	if err := json.Unmarshal(resp.Body(), &response); err != nil {
		return "", fmt.Errorf("ошибка при разборе ответа: %v", err)
	}

	// Проверка на ошибки в ответе
	if retCode, ok := response["retCode"].(float64); !ok || retCode != 0 {
		return "", fmt.Errorf("ошибка API: %v", string(resp.Body()))
	}

	// Получение ID ордера
	result, ok := response["result"].(map[string]interface{})
	if !ok {
		return "", fmt.Errorf("неверный формат ответа: отсутствует поле result")
	}

	orderId, ok := result["orderId"].(string)
	if !ok {
		return "", fmt.Errorf("неверный формат ответа: отсутствует orderId")
	}

	return orderId, nil
}

// Обработчик сообщений WebSocket
func onMessage(msg []byte) {
	shutdownMutex.Lock()
	if orderPlaced >= maxOrders || shutdownRequested {
		shutdownMutex.Unlock()
		return
	}
	shutdownMutex.Unlock()

	var data map[string]interface{}
	if err := json.Unmarshal(msg, &data); err != nil {
		log.Println("Ошибка разбора JSON:", err)
		return
	}

	// Обработка статуса подписки
	if success, ok := data["success"].(bool); ok && success {
		return
	}

	if topic, ok := data["topic"].(string); ok {
		if strings.Contains(topic, "orderbook.1.") {
			// fmt.Println("L1 данные")
			// Обработка L1 данных для обновления границ
			if orderbookData, ok := data["data"].(map[string]interface{}); ok {
				updatePriceBoundaries(orderbookData)
			}
		} else {
			// Обработка L50/L200 данных
			// fmt.Println("L50/L200 данные")
			if orderbookData, ok := data["data"].(map[string]interface{}); ok {
				switch lastOderType {
				case "Buy":
					handleSellScenario(orderbookData)
				case "Sell":
					handleBuyScenario(orderbookData)
				default:
					if handleBuyScenario(orderbookData) {
						return
					}
					if handleSellScenario(orderbookData) {
						return
					}
				}
			}
		}
	}
}

// Обновление границ цен на основе L1 данных
func updatePriceBoundaries(data map[string]interface{}) {
	if bids, ok := data["b"].([]interface{}); ok && len(bids) > 0 {
		if asks, ok := data["a"].([]interface{}); ok && len(asks) > 0 {
			bestBidData := bids[0].([]interface{})
			bestAskData := asks[0].([]interface{})

			bestBidPrice, _ := strconv.ParseFloat(bestBidData[0].(string), 64)
			bestAskPrice, _ := strconv.ParseFloat(bestAskData[0].(string), 64)

			mediumPrice = (bestBidPrice + bestAskPrice) / 2

			// Расчет минимальных границ отклонения
			minDeviation := mediumPrice * minPriceDeviation / 100

			// Расчет максимальных границ отклонения с использованием динамических значений
			maxDeviationBid := mediumPrice * maxPriceDeviationBid / 100
			maxDeviationAsk := mediumPrice * maxPriceDeviationAsk / 100

			// Минимальные границы (ближе к срединной цене)
			bidDeviationPriceMin = mediumPrice - minDeviation
			askDeviationPriceMin = mediumPrice + minDeviation

			// Максимальные границы (дальше от срединной цены) - теперь динамические
			bidDeviationPriceMax = mediumPrice - maxDeviationBid
			askDeviationPriceMax = mediumPrice + maxDeviationAsk
		}
	}
}

// Функция для обработки покупки
func handleBuyScenario(orderbookData map[string]interface{}) bool {
	if bids, ok := orderbookData["b"].([]interface{}); ok && len(bids) > 0 {
		// Получаем лучший бид для установки текущей цены монеты
		bestBidData := bids[0].([]interface{})
		bestBidPrice, _ := strconv.ParseFloat(bestBidData[0].(string), 64)
		coinPriceInUsdt = bestBidPrice

		// Проверяем каждый бид
		for _, bid := range bids {
			bidData := bid.([]interface{})
			priceStr := bidData[0].(string)
			volStr := bidData[1].(string)

			price, _ := strconv.ParseFloat(priceStr, 64)
			vol, _ := strconv.ParseFloat(volStr, 64)

			// Проверяем объем и границы цены одновременно
			volumeThreshold := volPerelivashaUsdt
			if orderPlaced > 0 {
				volumeThreshold = volPerelivashaUsdt * multiplierPerelivasha
			}
			// Проверяем, что цена находится в пределах MIN и MAX границ отклонения
			if vol*price > volumeThreshold && price >= bidDeviationPriceMax && price <= bidDeviationPriceMin {
				entriPrice := price
				orderPrice := round(entriPrice+shagCeni*shagCeniMultiplier, nulAfter)
				fmt.Println("Действие:", Bold+Green+"покупка"+Reset)
				qty := int(math.Max(float64(qtyUsdt)/coinPriceInUsdt, minOrderQty))
				placeOrder(orderPrice, qty, "Buy")
				lastOderType = "Buy"

				// Сохраняем цену ордера манипулятора для последующего уменьшения MAX_PRICE_DEVIATION
				lastManipulatorOrderPriceBid = entriPrice

				// Если включено уменьшение MAX_PRICE_DEVIATION, уменьшаем его для bid стороны
				if enableMaxPriceDeviationReduction {
					// Уменьшаем MAX_PRICE_DEVIATION на основе расстояния от срединной цены до ордера манипулятора
					deviationFromMedium := math.Abs(mediumPrice-entriPrice) / mediumPrice * 100
					if deviationFromMedium < maxPriceDeviationBid {
						maxPriceDeviationBid = deviationFromMedium
						log.Printf("MAX_PRICE_DEVIATION для bids уменьшен до %.4f%% (цена манипулятора: %.8f)", maxPriceDeviationBid, entriPrice)
					}
				}

				orderPlaced += 1
				if orderPlaced == maxOrders {
					shutdownMutex.Lock()
					shutdownRequested = true
					shutdownMutex.Unlock()
					return true
				}
				return true
			}
		}
	}
	return false
}

// Функция для обработки продажи
func handleSellScenario(orderbookData map[string]interface{}) bool {
	if asks, ok := orderbookData["a"].([]interface{}); ok && len(asks) > 0 {
		// Получаем лучший аск для установки текущей цены монеты
		bestAskData := asks[0].([]interface{})
		bestAskPrice, _ := strconv.ParseFloat(bestAskData[0].(string), 64)
		coinPriceInUsdt = bestAskPrice

		// Проверяем каждый аск
		for _, ask := range asks {
			askData := ask.([]interface{})
			priceStr := askData[0].(string)
			volStr := askData[1].(string)

			price, _ := strconv.ParseFloat(priceStr, 64)
			vol, _ := strconv.ParseFloat(volStr, 64)

			// Проверяем объем и границы цены одновременно
			volumeThreshold := volPerelivashaUsdt
			if orderPlaced > 0 {
				volumeThreshold = volPerelivashaUsdt * multiplierPerelivasha
			}
			// Проверяем, что цена находится в пределах MIN и MAX границ отклонения
			if vol*price > volumeThreshold && price <= askDeviationPriceMax && price >= askDeviationPriceMin {
				entriPrice := price
				orderPrice := round(entriPrice-shagCeni*shagCeniMultiplier, nulAfter)
				fmt.Println("Действие:", Bold+Red+"продажа"+Reset)
				qty := int(math.Max(float64(qtyUsdt)/coinPriceInUsdt, minOrderQty))
				placeOrder(orderPrice, qty, "Sell")
				lastOderType = "Sell"

				// Сохраняем цену ордера манипулятора для последующего уменьшения MAX_PRICE_DEVIATION
				lastManipulatorOrderPriceAsk = entriPrice

				// Если включено уменьшение MAX_PRICE_DEVIATION, уменьшаем его для ask стороны
				if enableMaxPriceDeviationReduction {
					// Уменьшаем MAX_PRICE_DEVIATION на основе расстояния от срединной цены до ордера манипулятора
					deviationFromMedium := math.Abs(entriPrice-mediumPrice) / mediumPrice * 100
					if deviationFromMedium < maxPriceDeviationAsk {
						maxPriceDeviationAsk = deviationFromMedium
						log.Printf("MAX_PRICE_DEVIATION для asks уменьшен до %.4f%% (цена манипулятора: %.8f)", maxPriceDeviationAsk, entriPrice)
					}
				}

				orderPlaced += 1
				if orderPlaced == maxOrders {
					shutdownMutex.Lock()
					shutdownRequested = true
					shutdownMutex.Unlock()
					return true
				}
				return true
			}
		}
	}
	return false
}

// Подключение к WebSocket и подписка на поток
func connectWebSocket() {
	url := "wss://stream.bybit.com/v5/public/linear"
	conn, _, err := websocket.DefaultDialer.Dial(url, nil)
	if err != nil {
		log.Fatal("Ошибка подключения к WebSocket:", err)
	}
	defer conn.Close()

	// Формируем список подписок на основе флагов
	var subscribeMessages []map[string]interface{}

	if useLevel1 {
		subscribeMessages = append(subscribeMessages, map[string]interface{}{
			"op":   "subscribe",
			"args": []string{fmt.Sprintf("orderbook.1.%s", symbol)},
		})
	}
	if useLevel50 {
		subscribeMessages = append(subscribeMessages, map[string]interface{}{
			"op":   "subscribe",
			"args": []string{fmt.Sprintf("orderbook.50.%s", symbol)},
		})
	}
	if useLevel200 {
		subscribeMessages = append(subscribeMessages, map[string]interface{}{
			"op":   "subscribe",
			"args": []string{fmt.Sprintf("orderbook.200.%s", symbol)},
		})
	}

	// Проверяем, что хотя бы один уровень выбран
	if len(subscribeMessages) == 0 {
		log.Fatal("Необходимо выбрать хотя бы один уровень ордербука (Level 1, Level 50 или Level 200)")
	}

	// Подписываемся на выбранные потоки
	for _, msg := range subscribeMessages {
		if err := conn.WriteJSON(msg); err != nil {
			log.Fatal("Ошибка при подписке:", err)
		}
	}

	// Обработка сообщений
	for {
		// Проверяем, нужно ли завершать работу
		shutdownMutex.Lock()
		shouldShutdown := shutdownRequested
		shutdownMutex.Unlock()

		if shouldShutdown {
			log.Println("Получен сигнал завершения, закрываем WebSocket соединение...")
			break
		}

		conn.SetReadDeadline(time.Now().Add(1 * time.Second))
		_, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Println("Ошибка при чтении сообщения:", err)
				break
			}
			// Timeout - это нормально, продолжаем
			continue
		}
		onMessage(message)
	}
}

// Функция для выставления прогревочного ордера
func placePreheatOrder() error {
	// Получаем текущие цены
	symbolV5 := bybit.SymbolV5(symbol)
	resp, err := bybitClient.V5().Market().GetTickers(bybit.V5GetTickersParam{
		Category: bybit.CategoryV5Linear,
		Symbol:   &symbolV5,
	})
	if err != nil {
		return fmt.Errorf("ошибка получения тикера: %v", err)
	}

	if len(resp.Result.LinearInverse.List) == 0 {
		return fmt.Errorf("не найдены данные тикера для %s", symbol)
	}

	// Получаем текущую цену
	lastPrice, err := strconv.ParseFloat(resp.Result.LinearInverse.List[0].LastPrice, 64)
	if err != nil {
		return fmt.Errorf("ошибка преобразования цены: %v", err)
	}

	// Рассчитываем цену для прогревочного ордера (ниже текущей на progrevPercent процентов)
	preheatPrice := lastPrice * (1 - progrevPercent/100)
	preheatPrice = round(preheatPrice, nulAfter)

	// Рассчитываем количество в контрактах
	qty := int(math.Max(float64(qtyProgrevUsdt)/lastPrice, minOrderQty))

	fmt.Printf("Выставляем прогревочный ордер: цена=%.8f, количество=%d\n", preheatPrice, qty)
	placeOrder(preheatPrice, qty, "Buy")

	return nil
}

// Функция для циклического выполнения прогревочных ордеров
func runPreheatLoop() {
	// Выставляем первый прогревочный ордер сразу
	if err := placePreheatOrder(); err != nil {
		log.Printf("Ошибка выставления первого прогревочного ордера: %v", err)
		return
	}
	fmt.Println("Первый прогревочный ордер выставлен успешно")

	// Запускаем цикл с заданным интервалом
	ticker := time.NewTicker(time.Duration(progrevInterval) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := placePreheatOrder(); err != nil {
				log.Printf("Ошибка выставления циклического прогревочного ордера: %v", err)
			} else {
				fmt.Printf("Циклический прогревочный ордер выставлен успешно (интервал: %d сек)\n", progrevInterval)
			}
		}
	}
}

// Основная функция
func main() {
	fmt.Println("# Frontrunner")
	fmt.Println("Версия: 0.0.25")
	fmt.Println("Автор: https://t.me/suenot")
	fmt.Println("---")
	fmt.Printf("Symbol: %s \n", symbol)
	fmt.Printf("Quantity: %d USDT\n", qtyUsdt)
	fmt.Printf("Quantity progrev: %d \n", qtyProgrevUsdt)
	fmt.Printf("Progrev percent: %.2f \n", progrevPercent)
	fmt.Printf("Progrev loop: %v \n", progrevLoop)
	if progrevLoop {
		fmt.Printf("Progrev interval: %d seconds\n", progrevInterval)
	}
	fmt.Printf("Volume perelivasha: %.2f \n", volPerelivashaUsdt)
	fmt.Printf("Multiplier perelivasha: %.8f \n", multiplierPerelivasha)
	fmt.Printf("Shag ceni: %.8f \n", shagCeni)
	fmt.Printf("Shag ceni multiplier: %.2f \n", shagCeniMultiplier)
	fmt.Printf("Nul after: %d \n", nulAfter)
	fmt.Printf("Min order qty: %.0f contracts\n", minOrderQty)
	fmt.Printf("Qty step: %.0f contracts\n", qtyStep)
	fmt.Printf("Max price deviation: %.2f%%\n", maxPriceDeviation)
	fmt.Printf("Min price deviation: %.2f%%\n", minPriceDeviation)
	fmt.Printf("Max orders: %d\n", maxOrders)
	fmt.Printf("Using orderbook levels: L1=%v L50=%v L200=%v\n", useLevel1, useLevel50, useLevel200)
	fmt.Printf("Using direct API (fasthttp): %v\n", directApi)
	if directApi {
		fmt.Printf("Recv window: %s ms\n", recvWindow)
	}
	fmt.Printf("Enable MAX_PRICE_DEVIATION reduction: %v\n", enableMaxPriceDeviationReduction)
	fmt.Printf("Cancel old orders mode: %s\n", cancelOldOrders)
	if cancelOldOrders == "remove_after_timeout" {
		fmt.Printf("Cancel timeout: %.1f seconds\n", cancelTimeoutSeconds)
	}

	// Запускаем горутину для периодической обработки старых ордеров
	if cancelOldOrders == "remove_after_timeout" {
		wg.Add(1)
		go func() {
			defer wg.Done()
			tickerInterval := time.Duration(cancelTimeoutSeconds/2*1000) * time.Millisecond
			ticker := time.NewTicker(tickerInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ticker.C:
					handleOldOrders()
					// Проверяем, нужно ли завершать работу
					shutdownMutex.Lock()
					if shutdownRequested && len(activeOrders) == 0 {
						shutdownMutex.Unlock()
						return
					}
					shutdownMutex.Unlock()
				}
			}
		}()
	}

	// Выставляем прогревочный ордер
	if qtyProgrevUsdt > 0 {
		if progrevLoop {
			// Запускаем циклический режим в отдельной горутине
			go runPreheatLoop()
		} else {
			// Выставляем одноразовый прогревочный ордер
			if err := placePreheatOrder(); err != nil {
				log.Fatalf("Ошибка выставления прогревочного ордера: %v", err)
			}
			fmt.Println("Прогревочный ордер выставлен успешно")
		}
	}

	// Настройка обработки сигналов для graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Запускаем WebSocket в отдельной горутине
	go connectWebSocket()

	// Ожидаем сигнал завершения или достижения максимального количества ордеров
	go func() {
		<-sigChan
		log.Println("Получен сигнал завершения...")
		shutdownMutex.Lock()
		shutdownRequested = true
		shutdownMutex.Unlock()
	}()

	// Ожидаем завершения работы
	for {
		shutdownMutex.Lock()
		shouldShutdown := shutdownRequested
		shutdownMutex.Unlock()

		if shouldShutdown {
			if cancelOldOrders == "remove_after_timeout" {
				log.Println("Ожидаем завершения обработки старых ордеров...")
				wg.Wait()
				log.Println("Все старые ордера обработаны.")
			}
			break
		}
		time.Sleep(100 * time.Millisecond)
	}

	log.Println("Программа завершена.")
}

// Вспомогательная функция для округления до указанного количества знаков после запятой
func round(value float64, precision int) float64 {
	scale := math.Pow(10, float64(precision))
	return math.Round(value*scale) / scale
}
